# 后端.txt  完整代码（含【新增】【修改】）
from flask import Flask, request, jsonify, session
import secrets, datetime, mysql.connector
from flask_cors import CORS   # 新增

app = Flask(__name__)
CORS(app)
app.secret_key = 'simple_key'

def create_conn():
    return mysql.connector.connect(
        host='localhost',
        database='School',
        user='root',
        password='lx123456',
        port=3306,
        charset='utf8mb4'
    )

# -------------------- 原有接口 --------------------
@app.route('/check_phone', methods=['GET'])
def check_phone():
    phone = request.args.get('phone', '').strip()
    if not phone:
        return jsonify(code=1, message='手机号不能为空'), 400
    conn = create_conn(); cur = conn.cursor()
    cur.execute("SELECT id FROM school_users WHERE phone=%s", (phone,))
    exists = cur.fetchone() is not None
    cur.close(); conn.close()
    if exists:
        token = secrets.token_hex(16)
        session[token] = phone
        return jsonify(code=0, message='存在', token=token), 200
    else:
        return jsonify(code=1, message='手机号不存在'), 404

@app.route('/reset_pwd', methods=['POST'])
def reset_pwd():
    data = request.json
    token = data.get('token', '').strip()
    pwd = data.get('password', '').strip()
    if not token or not pwd:
        return jsonify(code=1, message='参数不完整'), 400
    if not (6 <= len(pwd) <= 12):
        return jsonify(code=2, message='密码长度6-12位'), 400
    conn = create_conn(); cur = conn.cursor()
    cur.execute("UPDATE school_users SET password=%s WHERE phone=%s", (pwd, token))
    conn.commit(); affected = cur.rowcount
    cur.close(); conn.close()
    return jsonify(code=0 if affected else 4,
                   message='密码已更新' if affected else '更新失败'), 200

@app.route('/login', methods=['POST'])
def login():
    data = request.json
    account = data.get('account', '').strip()
    pwd = data.get('password', '').strip()
    conn = create_conn(); cur = conn.cursor(dictionary=True)
    cur.execute("SELECT * FROM school_users WHERE username=%s AND password=%s", (account, pwd))
    user = cur.fetchone()
    cur.close(); conn.close()
    return jsonify(code=0 if user else 1,
                   message='登录成功' if user else '用户名或密码错误'), 200

@app.route('/register', methods=['POST'])
def register():
    data = request.json
    username = data.get('username', '').strip()
    pwd = data.get('password', '').strip()
    confirm = data.get('confirm', '').strip()
    phone = data.get('phone', '').strip()
    if any(not v for v in [username, pwd, confirm, phone]):
        return jsonify(code=1, msg='必填项不能为空'), 400
    if not (6 <= len(pwd) <= 12):
        return jsonify(code=2, msg='密码长度应为 6-12 位'), 400
    if pwd != confirm:
        return jsonify(code=3, msg='两次密码不一致'), 400
    conn = create_conn(); cur = conn.cursor()
    cur.execute("SELECT id FROM school_users WHERE username=%s OR phone=%s", (username, phone))
    if cur.fetchone():
        return jsonify(code=4, msg='用户名或手机号已存在'), 409
    cur.execute("INSERT INTO school_users(username, password, phone) VALUES(%s,%s,%s)",
                (username, pwd, phone))
    conn.commit(); cur.close(); conn.close()
    return jsonify(code=0, msg='注册成功'), 201

# -------------------- 新增接口 --------------------
# 1. 随机生成并返回动态列表
@app.route('/posts', methods=['GET'])
def get_posts():
    conn = create_conn(); cur = conn.cursor(dictionary=True)
    # 首次运行自动插入几条假数据
    cur.execute("SELECT COUNT(*) AS c FROM posts")
    if cur.fetchone()['c'] == 0:
        fake = [
            (1, 'Alice', None, '毕业快乐', None, 66, 3, 4),
            (1, 'Bob',   None, '长江大学', None, 77, 5, 2),
            (1, 'Cindy', None, '校园活动', None, 88, 2, 1)
        ]
        cur.executemany("""INSERT INTO posts(user_id, username, content,
                           likes_count, comments_count, shares_count)
                           VALUES (%s,%s,%s,%s,%s,%s)""", fake)
        conn.commit()
    cur.execute("SELECT * FROM posts ORDER BY created_at DESC")
    posts = cur.fetchall()
    # 把 datetime 转字符串
    for p in posts:
        p['created_at'] = p['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    cur.close(); conn.close()
    return jsonify(posts)

# 2. 获取单条动态的评论
@app.route('/posts/<int:pid>/comments', methods=['GET'])
def get_comments(pid):
    conn = create_conn(); cur = conn.cursor(dictionary=True)
    cur.execute("SELECT * FROM comments WHERE post_id=%s ORDER BY created_at ASC", (pid,))
    cmts = cur.fetchall()
    for c in cmts:
        c['created_at'] = c['created_at'].strftime('%m-%d %H:%M')
    cur.close(); conn.close()
    return jsonify(cmts)

# 3. 发表评论
@app.route('/posts/<int:pid>/comments', methods=['POST'])
def add_comment(pid):
    data = request.json
    username = data.get('username', '匿名')
    content = data.get('content', '').strip()
    if not content:
        return jsonify(code=1, message='内容为空'), 400
    conn = create_conn(); cur = conn.cursor()
    cur.execute("""INSERT INTO comments(post_id, username, content)
                   VALUES (%s,%s,%s)""", (pid, username, content))
    cur.execute("UPDATE posts SET comments_count = comments_count + 1 WHERE id=%s", (pid,))
    conn.commit(); cur.close(); conn.close()
    return jsonify(code=0, message='ok')

# 4. 给动态点赞
@app.route('/posts/<int:pid>/like', methods=['POST'])
def like_post(pid):
    conn = create_conn(); cur = conn.cursor()
    cur.execute("UPDATE posts SET likes_count = likes_count + 1 WHERE id=%s", (pid,))
    conn.commit(); cur.close(); conn.close()
    return jsonify(code=0, message='ok')

# 5. 给评论点赞
@app.route('/comments/<int:cid>/like', methods=['POST'])
def like_comment(cid):
    conn = create_conn(); cur = conn.cursor()
    cur.execute("UPDATE comments SET likes_count = likes_count + 1 WHERE id=%s", (cid,))
    conn.commit(); cur.close(); conn.close()
    return jsonify(code=0, message='ok')

# 6. 取消动态点赞
@app.route('/posts/<int:pid>/unlike', methods=['POST'])
def unlike_post(pid):
    conn = create_conn(); cur = conn.cursor()
    cur.execute("UPDATE posts SET likes_count = GREATEST(likes_count - 1, 0) WHERE id=%s", (pid,))
    conn.commit(); cur.close(); conn.close()
    return jsonify(code=0, message='ok')

# 7. 取消评论点赞
@app.route('/comments/<int:cid>/unlike', methods=['POST'])
def unlike_comment(cid):
    conn = create_conn(); cur = conn.cursor()
    cur.execute("UPDATE comments SET likes_count = GREATEST(likes_count - 1, 0) WHERE id=%s", (cid,))
    conn.commit(); cur.close(); conn.close()
    return jsonify(code=0, message='ok')

# -------------------- 学生成绩查询系统接口 --------------------
# 8. 学生登录验证
@app.route('/student/login', methods=['POST'])
def student_login():
    data = request.json
    name = data.get('name', '').strip()
    card_number = data.get('card_number', '').strip()
    password = data.get('password', '').strip()

    if not all([name, card_number, password]):
        return jsonify(code=1, message='请完整填写信息'), 400

    try:
        conn = create_conn()
        cur = conn.cursor(dictionary=True)
        cur.execute("""
            SELECT id, name, card_number
            FROM students
            WHERE name=%s AND card_number=%s AND password=%s
        """, (name, card_number, password))
        student = cur.fetchone()
        cur.close()
        conn.close()

        if student:
            return jsonify(code=0, message='登录成功', data=student), 200
        else:
            return jsonify(code=1, message='姓名、卡号或密码错误'), 401
    except Exception as e:
        return jsonify(code=2, message='系统错误'), 500

# 9. 获取学生成绩
@app.route('/student/<int:student_id>/scores', methods=['GET'])
def get_student_scores(student_id):
    try:
        conn = create_conn()
        cur = conn.cursor(dictionary=True)
        cur.execute("""
            SELECT s.name, s.card_number, sc.course_name, sc.score, sc.class_rank
            FROM students s
            JOIN scores sc ON s.id = sc.student_id
            WHERE s.id = %s
            ORDER BY sc.course_name
        """, (student_id,))
        scores = cur.fetchall()
        cur.close()
        conn.close()

        if scores:
            return jsonify(code=0, message='获取成功', data=scores), 200
        else:
            return jsonify(code=1, message='未找到成绩信息'), 404
    except Exception as e:
        return jsonify(code=2, message='系统错误'), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=6767, debug=True)